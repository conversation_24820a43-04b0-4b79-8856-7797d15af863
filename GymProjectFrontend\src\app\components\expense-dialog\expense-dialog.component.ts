import { Component, Inject, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { ExpenseService } from '../../services/expense.service';
import { Expense } from '../../models/expense.model';
import { ExpenseDto } from '../../models/expenseDto.model';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-expense-dialog',
  templateUrl: './expense-dialog.component.html',
  styleUrls: ['./expense-dialog.component.css'],
  standalone: false
})
export class ExpenseDialogComponent implements OnInit {

  expenseForm: FormGroup;
  isEditMode: boolean = false;
  isLoading: boolean = false;

  expenseTypes: string[] = [
    'Fatura - Elektrik', '<PERSON>ura - Su', '<PERSON>ura - Doğalgaz', 'Fatura - İnternet',
    '<PERSON><PERSON>ş Ödemesi', '<PERSON>', '<PERSON>ze<PERSON> Alımı', 'Temizlik Malzemesi',
    '<PERSON>is Gideri', 'Bakım/Onarım', 'Vergi/Harç', 'Diğer'
  ];

  constructor(
    private fb: FormBuilder,
    public dialogRef: MatDialogRef<ExpenseDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ExpenseDto | null,
    private expenseService: ExpenseService,
    private toastrService: ToastrService
  ) {
    this.isEditMode = !!data;
    // FormGroup constructor içinde veya ngOnInit'de tanımlanmalı
    this.expenseForm = this.fb.group({}); // Boş başlat
  }

  ngOnInit(): void {
    this.createExpenseForm();
  }

  createExpenseForm(): void {
    // Tarih formatını HTML5 date input için hazırla (YYYY-MM-DD)
    let formattedDate = '';
    if (this.data?.expenseDate) {
      const date = new Date(this.data.expenseDate);
      formattedDate = date.toISOString().split('T')[0];
    } else {
      // Yeni gider için bugünün tarihini varsayılan olarak ayarla
      const today = new Date();
      formattedDate = today.toISOString().split('T')[0];
    }

    this.expenseForm = this.fb.group({
      expenseID: [this.data?.expenseID || 0],
      description: [this.data?.description || '', Validators.maxLength(500)], // İsteğe bağlı
      amount: [this.data?.amount || null, [Validators.required, Validators.min(0.01)]],
      expenseDate: [formattedDate, Validators.required], // HTML5 date input için string format
      expenseType: [this.data?.expenseType || '', [Validators.required, Validators.maxLength(100)]]
    });
  }

  saveExpense(): void {
    if (this.expenseForm.invalid) {
      this.toastrService.warning('Lütfen formdaki tüm zorunlu alanları doğru şekilde doldurun.', 'Uyarı');
      this.expenseForm.markAllAsTouched();
      return;
    }

    this.isLoading = true;

    // HTML5 date input'tan gelen string tarihi Date nesnesine çevir
    const dateString: string = this.expenseForm.value.expenseDate;
    let utcDate: Date | null = null;

    if (dateString) {
        // YYYY-MM-DD formatındaki string'i Date nesnesine çevir
        const [year, month, day] = dateString.split('-').map(Number);
        // UTC olarak ilgili günün gece yarısını temsil eden yeni bir Date nesnesi oluştur
        utcDate = new Date(Date.UTC(year, month - 1, day, 0, 0, 0, 0)); // month - 1 çünkü JS'de aylar 0-11 arası
    } else {
        this.toastrService.error('Geçersiz tarih seçimi.', 'Hata');
        this.isLoading = false;
        return;
    }

    const expenseData: Expense = {
      // Formdaki diğer değerleri alırken expenseDate'i hariç tut
      expenseID: this.expenseForm.value.expenseID,
      description: this.expenseForm.value.description,
      amount: this.expenseForm.value.amount,
      expenseType: this.expenseForm.value.expenseType,
      // Hesaplanan UTC tarihini ata
      expenseDate: utcDate,
      companyID: 0, // Backend'de atanacak
      isActive: true, // Backend'de yönetilebilir
      creationDate: this.data?.creationDate || new Date() // Interface'e uymak için eklendi
    };

     // Eğer description boşsa undefined ata
     if (!expenseData.description?.trim()) {
      expenseData.description = undefined;
    }

    const request$: Observable<any> = this.isEditMode
      ? this.expenseService.update(expenseData)
      : this.expenseService.add(expenseData);

    request$.subscribe({
      next: (response) => {
        if (response.success) {
          this.toastrService.success(this.isEditMode ? 'Gider başarıyla güncellendi.' : 'Gider başarıyla eklendi.', 'Başarılı');
          this.dialogRef.close(true);
        } else {
          this.toastrService.error(response.message || 'İşlem sırasında bir hata oluştu.', 'Hata');
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error saving expense:', error);
        const errorMessage = error.error?.message || error.error?.Message || error.message || 'İşlem sırasında bir sunucu hatası oluştu.';
        this.toastrService.error(errorMessage, 'Hata');
        this.isLoading = false;
      }
    });
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  // Form kontrollerine kolay erişim için getter'lar
  get description() { return this.expenseForm.get('description'); }
  get amount() { return this.expenseForm.get('amount'); }
  get expenseDate() { return this.expenseForm.get('expenseDate'); }
  get expenseType() { return this.expenseForm.get('expenseType'); }
}